// Place your Google OAuth client IDs here.
//
// - kGoogleServerClientId: REQUIRED on Android for google_sign_in v7+.
//   Use the OAuth 2.0 Client ID of type "Web application" from Google Cloud Console.
// - kGoogleClientIdIOS: OPTIONAL; if not provided, the plugin may read from GoogleService-Info.plist.
//
// Important: You do NOT need to ship client secret in the app.

const String kGoogleServerClientId = '267438226093-9ogu9phbvvvsvafncoqtrt1v1qpudplc.apps.googleusercontent.com';
const String? kGoogleClientIdIOS = null;

