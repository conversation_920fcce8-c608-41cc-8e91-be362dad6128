import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../services/auth_service.dart';
import '../utils/pdf_export.dart';
import '../widgets/lo_sho_grid.dart';
import '../widgets/proposed_mobile_analysis.dart';
import '../widgets/generated_table.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import '../helpers/math_helpers.dart';


class MyHomePage extends StatefulWidget {
  final String title;
  final Map<String, dynamic>? entry; // Accepts history entry (optional)

  const MyHomePage({super.key, required this.title, this.entry});

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}


class _MyHomePageState extends State<MyHomePage> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _dobController = TextEditingController();
  final TextEditingController _mobileController = TextEditingController();
  final TextEditingController _proposedMobileController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  bool isTableVisible = false;
  late String _gender = "Male"; // Default gender is Male
  List<List<List<int>>> allTableValues = [];
  Map<String, dynamic> loShoGridValues = {};
  List<Map<String, dynamic>> historyEntries = [];

  @override
  void initState() {
    super.initState();

    if (widget.entry != null) {
      _nameController.text = widget.entry!['name'];
      _dobController.text = widget.entry!['dob'];
      _mobileController.text = widget.entry!['mobile'] == 'N/A' ? '' : widget.entry!['mobile'];
      _proposedMobileController.text = widget.entry!['proposedMobile'] == 'N/A' ? '' : widget.entry!['proposedMobile'];
      _gender = widget.entry!['gender'];
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _dobController.dispose();
    _mobileController.dispose();
    _proposedMobileController.dispose();
    super.dispose();
  }

  Map<String, dynamic> generateLoShoGrid(String dob, String gender) {
    List<String> dobParts = dob.split('/');
    if (dobParts.length != 3) {
      throw Exception('Invalid DOB format. Please use DD/MM/YYYY format.');
    }

    int day = int.parse(dobParts[0]);
    int month = int.parse(dobParts[1]);
    int year = int.parse(dobParts[2]);

    int s = day ~/ 10;
    int t = day % 10;
    int u = month ~/ 10;
    int v = month % 10;
    int w = year ~/ 1000;
    int x = (year % 1000) ~/ 100;
    int y = (year % 100) ~/ 10;
    int z = year % 10;

    int driver = digitRoot(s + t);
    int conductor = digitRoot(s + t + u + v + w + x + y + z);
    int kua = digitRoot(w + x + y + z);

    if (gender == "Male") {
      kua = digitRoot((kua - 11).abs()); // Remove negative sign
    } else if (gender == "Female") {
      kua = digitRoot(kua + 4);
    }

    // Lo Shu Grid Mapping (positions for numbers 1-9)
    Map<int, List<int>> loShoMapping = {
      4: [0, 0], 9: [0, 1], 2: [0, 2],
      3: [1, 0], 5: [1, 1], 7: [1, 2],
      8: [2, 0], 1: [2, 1], 6: [2, 2]
    };

    // Initialize a 3x3 grid with nulls (empty slots)
    List<List<int>> grid = List.generate(3, (_) => List.generate(3, (_) => 0));

    // Numbers to be placed in the grid
    List<int> numbers = [s, t, u, v, w, x, y, z, conductor, kua];

    // Check if driver should be added
    bool shouldAddDriver = !((day >= 1 && day <= 10) || day == 20 || day == 30 );
    if (shouldAddDriver) {
      numbers.add(driver);
    }

    // Fill the grid
    for (int number in numbers) {
      if (loShoMapping.containsKey(number)) {
        int row = loShoMapping[number]![0];
        int col = loShoMapping[number]![1];

        // Append number to the existing value in the grid (concatenation logic)
        if (grid[row][col] == 0) {
          grid[row][col] = number; // First occurrence
        } else {
          grid[row][col] = int.parse('${grid[row][col]}$number'); // Append for repetition
        }
      }
    }

    // Calculate Lottery Number (Exclude Driver if not added)
    Map<String, dynamic> lotteryData = calculateLotteryNumber(shouldAddDriver ? driver : null, conductor, kua, grid);

    // Chaldean Numerology mapping
    Map<String, int> chaldeanTable = {
      'A': 1, 'I': 1, 'J': 1, 'Q': 1, 'Y': 1,
      'B': 2, 'K': 2, 'R': 2,
      'C': 3, 'G': 3, 'L': 3, 'S': 3,
      'D': 4, 'M': 4, 'T': 4,
      'E': 5, 'H': 5, 'N': 5, 'X': 5,
      'U': 6, 'V': 6, 'W': 6,
      'O': 7, 'Z': 7,
      'F': 8, 'P': 8
    };

    // Function to sanitize name (removes spaces, special characters, converts to lowercase)
    String sanitizeName(String name) {
      return name.replaceAll(RegExp(r'[^a-zA-Z]'), '').toLowerCase();
    }

    // Function to calculate Chaldean numerology for a name
    int getChaldeanNumerology(String name) {
      name = sanitizeName(name); // Sanitize input
      int totalSum = 0;

      // Sum up Chaldean values
      for (int i = 0; i < name.length; i++) {
        String letter = name[i].toUpperCase(); // Convert each letter to uppercase for mapping
        if (chaldeanTable.containsKey(letter)) {
          totalSum += chaldeanTable[letter]!;
        }
      }

      // Reduce to single digit using digitRoot function
      return digitRoot(totalSum);
    }

    return {
      "grid": grid,
      "driver": driver,
      "conductor": conductor,
      "kua": kua,
      "lotteryNumber": lotteryData["lotteryNumber"], // Extracted string value
      "unrepeatedNumbers": lotteryData["unrepeatedNumbers"], // Extracted list
      "nameNumber": getChaldeanNumerology(_nameController.text)
    };

  }

  Map<String, dynamic> calculateLotteryNumber(int? driver, int conductor, int kua, List<List<int?>> loShoGrid) {
    Map<int, int> frequencyMap = {};
    List<int> keyNumbers = [conductor, kua]; // Exclude driver by default

    if (driver != null) {
      keyNumbers.add(driver); // Add driver only if it's not null
    }

    // Count occurrences of key numbers in the Lo Shu Grid
    for (var row in loShoGrid) {
      for (var cell in row) {
        if (cell != null) {
          frequencyMap[cell] = (frequencyMap[cell] ?? 0) + 1;
        }
      }
    }

    // Filter numbers that appear only once
    List<int> unrepeatedNumbers = keyNumbers.where((n) => frequencyMap[n] == 1).toList();


    // Compute Lottery Number
    int count = unrepeatedNumbers.length;
    String lotteryNumber = "$count/3";

    return {
      "unrepeatedNumbers": unrepeatedNumbers,
      "lotteryNumber": lotteryNumber
    };
  }

  List<List<int>> generateOriginalTable(String name, String dob) {
    List<String> dobParts = dob.split('/');
    if (dobParts.length != 3) {
      throw Exception('Invalid DOB format. Please use DD/MM/YYYY format.');
    }

    int day = int.parse(dobParts[0]);
    int month = int.parse(dobParts[1]);
    int year = int.parse(dobParts[2]);

    int s = day ~/ 10;
    int t = day % 10;
    int u = month ~/ 10;
    int v = month % 10;
    int w = year ~/ 1000;
    int x = (year % 1000) ~/ 100;
    int y = (year % 100) ~/ 10;
    int z = year % 10;

    int A = u + v;
    int B = s + t;
    int C = y + z;
    int D = s + t + u + v + w + x + y + z;

    A = digitRoot(A);
    B = digitRoot(B);
    C = digitRoot(C);
    D = digitRoot(D);

    return [
      [A, B, C, D],
      [digitRoot(C - 2), digitRoot(D + 2), digitRoot(A - 2), digitRoot(B + 2)],
      [digitRoot(D + 1), digitRoot(C + 1), digitRoot(B - 1), digitRoot(A - 1)],
      [digitRoot(B + 1), digitRoot(A - 3), digitRoot(D + 3), digitRoot(C - 1)],
    ];
  }

  List<List<int>> generateKeyBasedTable(String name, String dob, List<List<int>> originalTable, int requiredKey) {
    List<String> dobParts = dob.split('/');
    if (dobParts.length != 3) {
      throw Exception('Invalid DOB format. Please use DD/MM/YYYY format.');
    }

    int day = int.parse(dobParts[0]);
    int month = int.parse(dobParts[1]);
    int year = int.parse(dobParts[2]);

    int s = day ~/ 10;
    int t = day % 10;
    int u = month ~/ 10;
    int v = month % 10;
    int w = year ~/ 1000;
    int x = (year % 1000) ~/ 100;
    int y = (year % 100) ~/ 10;
    int z = year % 10;

    int sumValue = originalTable[0][0] + originalTable[0][1] + originalTable[0][2] + originalTable[0][3];
    int otherValue = sumValue - requiredKey;

    int A = digitRoot(u + v);
    int B = digitRoot(s + t);
    int C = digitRoot(y + z);
    int D = digitRoot(s + t + u + v + w + x + y + z - otherValue);

    return [
      [A, B, C, D],
      [digitRoot(C - 2), digitRoot(D + 2), digitRoot(A - 2), digitRoot(B + 2)],
      [digitRoot(D + 1), digitRoot(C + 1), digitRoot(B - 1), digitRoot(A - 1)],
      [digitRoot(B + 1), digitRoot(A - 3), digitRoot(D + 3), digitRoot(C - 1)],
    ];
  }

  void _scrollDown() {
    double stepSize = 100.0; // Adjust step size if needed
    double targetScroll = _scrollController.offset + (4.6 * stepSize);

    _scrollController.animateTo(
      targetScroll,
      duration: const Duration(milliseconds: 500), // Smooth scrolling
      curve: Curves.easeOut,
    );
  }

  void onSubmit() async {
    try {
      String name = _nameController.text.trim();
      String dob = _dobController.text.trim();
      String mobileNumber = _mobileController.text.trim();
      String proposedMobile = _proposedMobileController.text.trim();
      String gender = _gender; // Get selected gender

      if (name.isEmpty || dob.isEmpty || gender.isEmpty) {
        throw Exception('All fields are required.');
      }

      // Validate Mobile Number (if entered, must be exactly 10 digits)
      if (mobileNumber.isNotEmpty && !RegExp(r'^\d{10}$').hasMatch(mobileNumber)) {
        throw Exception('Mobile number must be exactly 10 digits or left empty.');
      }

      // Validate Proposed Mobile Number (if entered, must be exactly 10 digits)
      if (proposedMobile.isNotEmpty && !RegExp(r'^\d{10}$').hasMatch(proposedMobile)) {
        throw Exception('Proposed Mobile number must be exactly 10 digits or left empty.');
      }

      // Do async work *outside* setState
      List<List<int>> originalTable = generateOriginalTable(name, dob);
      List<List<List<int>>> allTables = [];
      allTables.add(originalTable);

      for (int requiredKey = 1; requiredKey <= 9; requiredKey++) {
        List<List<int>> keyBasedTable = generateKeyBasedTable(name, dob, originalTable, requiredKey);
        allTables.add(keyBasedTable);
      }


      // Now, update state synchronously
      if (context.mounted) {
        setState(() {
          allTableValues.clear();
          allTableValues.addAll(allTables);
          loShoGridValues = generateLoShoGrid(dob, gender);
          isTableVisible = true;

          historyEntries.add({
            'Name': name,
            'DOB': dob,
            'Gender': gender,
            'Mobile': mobileNumber,
            'Proposed Mobile': proposedMobile.isNotEmpty ? proposedMobile : 'N/A',
          });
        });

        _scrollDown(); // Automatically scroll down after submit
      }

    } catch (e) {
      if (context.mounted) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(e.toString())),
          );
        });
      }
    }
  }

  void onReset() {
    setState(() {
      _nameController.clear();
      _dobController.clear();
      _mobileController.clear();
      _proposedMobileController.clear();
      _gender = '';  // Reset gender
      isTableVisible = false;  // Hide the tables
      allTableValues.clear();  // Clear the table values
    });
  }
  final _formKey = GlobalKey<FormState>();

  void _showProfileDialog(User user) {
    showDialog(
      context: context,
      builder: (_) {
        return AlertDialog(
          title: const Text('Profile'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (user.photoURL != null)
                Center(child: CircleAvatar(radius: 30, backgroundImage: NetworkImage(user.photoURL!))),
              if (user.photoURL != null) const SizedBox(height: 12),
              Text('Name: ${user.displayName ?? '-'}'),
              Text('Email: ${user.email ?? '-'}'),
              // Text('UID: ${user.uid}'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        toolbarHeight: 70, // Increases AppBar height
        leading: Padding(
          padding: const EdgeInsets.only(left: 16.0), // Adds padding to the left
          child: Image.asset(
            'assets/Numerology.jpg', // Replace with your logo path
            height: 40, // Adjust the height as needed
          ),
        ),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start, // Align text properly
          children: [
            const Text(
              'Numerology App',
              textAlign: TextAlign.center, // Ensures center alignment
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.deepPurple,
              ),
            ),
            const Text(
              'By: Bharathi L.B (+91 **********)',
              textAlign: TextAlign.center, // Ensures center alignment
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.purple,
              ),
            ),
          ],
        ),
        centerTitle: true,
        actions: [
          if (FirebaseAuth.instance.currentUser != null)
            PopupMenuButton<String>(
              tooltip: 'Account',
              icon: Builder(builder: (context) {
                final u = FirebaseAuth.instance.currentUser!;
                final url = u.photoURL;
                if (url != null) {
                  return CircleAvatar(radius: 14, backgroundImage: NetworkImage(url));
                }
                return const Icon(Icons.account_circle);
              }),
              onSelected: (value) async {
                final u = FirebaseAuth.instance.currentUser;
                if (value == 'profile' && u != null) {
                  _showProfileDialog(u);
                } else if (value == 'logout') {
                  await AuthService.instance.signOut();
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem<String>(
                  value: 'profile',
                  child: ListTile(
                    leading: Icon(Icons.person_outline),
                    title: Text('Profile'),
                  ),
                ),
                const PopupMenuDivider(),
                const PopupMenuItem<String>(
                  value: 'logout',
                  child: ListTile(
                    leading: Icon(Icons.logout),
                    title: Text('Logout'),
                  ),
                ),
              ],
            ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(  // Wrap everything inside a SingleChildScrollView
          controller: _scrollController, // Attach the scroll controller
          child: Form( // Add the Form widget here
            key: _formKey,
            autovalidateMode: AutovalidateMode.onUserInteraction,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: <Widget>[
                const SizedBox(height: 10),
                TextField(
                    controller: _nameController,
                    style: const TextStyle(color: Colors.deepPurpleAccent, fontWeight: FontWeight.bold), // Text color
                    decoration: InputDecoration(
                      label: RichText(
                        text: const TextSpan(
                          text: 'Name',
                          style: TextStyle(color: Colors.deepPurple, fontSize: 16),
                          children: [
                            TextSpan(text: ' *', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                      labelStyle: const TextStyle(color: Colors.deepPurple), // Label color
                      focusedBorder: const OutlineInputBorder(
                        borderSide: BorderSide(color: Colors.deepPurpleAccent, width: 2),
                      ),
                      enabledBorder: const OutlineInputBorder(
                        borderSide: BorderSide(color: Colors.deepPurpleAccent, width: 1),
                      ),
                    ),
                    onChanged: (value) {
                      if (value.isNotEmpty) {
                        String formattedText = value
                            .split(' ') // Split the text into words
                            .map((word) => word.isNotEmpty
                            ? word[0].toUpperCase() + word.substring(1).toLowerCase()
                            : '')
                            .join(' '); // Join the words back with spaces

                        if (formattedText != value) {
                          _nameController.value = TextEditingValue(
                            text: formattedText,
                            selection: TextSelection.collapsed(offset: formattedText.length),
                          );
                        }
                      }
                    }
                ),
                const SizedBox(height: 10),
                Row(
                  children: [
                    RichText(
                      text: const TextSpan(
                        text: 'Gender: ',
                        style: TextStyle(fontSize: 16, color: Colors.deepPurple),
                        children: [
                          TextSpan(text: '*', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                    Row(
                      children: [
                        Radio<String>(
                          value: 'Male',
                          groupValue: _gender,
                          onChanged: (String? value) {
                            setState(() {
                              _gender = value!;
                            });
                          },
                          activeColor: Colors.pinkAccent,
                        ),
                        const Text('Male', style: TextStyle(color: Colors.deepPurple)),
                        Radio<String>(
                          value: 'Female',
                          groupValue: _gender,
                          onChanged: (String? value) {
                            setState(() {
                              _gender = value!;
                            });
                          },
                          activeColor: Colors.pinkAccent,
                        ),
                        const Text('Female',style: TextStyle(color: Colors.deepPurple)),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                TextField(
                  controller: _dobController,
                  style: const TextStyle(color: Colors.deepPurpleAccent, fontWeight: FontWeight.bold),
                  decoration: InputDecoration(
                    label: RichText(
                      text: const TextSpan(
                        text: 'Date of Birth',
                        style: TextStyle(color: Colors.deepPurple, fontSize: 16),
                        children: [
                          TextSpan(text: ' *', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                    hintText: 'DD/MM/YYYY',
                    labelStyle: const TextStyle(color: Colors.deepPurple),
                    focusedBorder: const OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.deepPurpleAccent, width: 2),
                    ),
                    enabledBorder: const OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.deepPurpleAccent, width: 1),
                    ),
                    suffixIcon: const Icon(Icons.calendar_today, color: Colors.purple),
                  ),
                  onTap: () async {
                    FocusScope.of(context).requestFocus(FocusNode());
                    showCupertinoModalPopup(
                      context: context,
                      builder: (_) => Container(
                        height: 250,
                        color: CupertinoColors.systemBackground.resolveFrom(context),
                        child: CupertinoDatePicker(
                          mode: CupertinoDatePickerMode.date,
                          initialDateTime: DateTime.now(),
                          minimumDate: DateTime(1900),
                          maximumDate: DateTime(2100),
                          onDateTimeChanged: (DateTime pickedDate) {
                            setState(() {
                              _dobController.text = "${pickedDate.day.toString().padLeft(2, '0')}/${pickedDate.month.toString().padLeft(2, '0')}/${pickedDate.year}";
                            });
                          },
                        ),
                      ),
                    );
                  },
                ),
                const SizedBox(height: 10),
                TextFormField(
                  controller: _mobileController,
                  style: const TextStyle(color: Colors.deepPurpleAccent, fontWeight: FontWeight.bold),
                  decoration: const InputDecoration(
                    labelText: 'Mobile Number',
                    labelStyle: TextStyle(color: Colors.deepPurple),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.deepPurpleAccent, width: 2),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.deepPurpleAccent, width: 1),
                    ),
                  ),
                  keyboardType: TextInputType.phone,
                  maxLength: 10,
                  inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  /*validator: (value) {
                    if (value?.length != 10 || !RegExp(r'^\d{10}$').hasMatch(value!)) {
                      return 'Mobile number must be 10 digits';
                    }
                    return null;
                  },*/
                ),
                const SizedBox(height: 10),
                TextFormField(
                    controller: _proposedMobileController,
                    style: const TextStyle(color: Colors.deepPurpleAccent, fontWeight: FontWeight.bold),
                    decoration: const InputDecoration(
                      labelText: 'Proposed Mobile Number',
                      labelStyle: TextStyle(color: Colors.deepPurple),
                      focusedBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: Colors.deepPurpleAccent, width: 2),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: Colors.deepPurpleAccent, width: 1),
                      ),
                    ),
                    keyboardType: TextInputType.phone,
                    maxLength: 10,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly]
                ),
                const SizedBox(height: 10),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    ElevatedButton(
                      onPressed: () {
                        if (_formKey.currentState?.validate() == true) {
                          onSubmit(); // Call your submit function if valid
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        foregroundColor: Colors.white,
                        backgroundColor: Colors.deepPurple,
                        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 5,
                      ),
                      child: const Text('Submit', style: TextStyle(fontSize: 16)),
                    ),
                    const SizedBox(width: 20),
                    ElevatedButton(
                      onPressed: onReset,
                      style: ElevatedButton.styleFrom(
                        foregroundColor: Colors.white,
                        backgroundColor: Colors.redAccent,
                        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 5,
                      ),
                      child: const Text('Reset', style: TextStyle(fontSize: 16)),
                    )
                  ],
                ),
                const SizedBox(height: 16),
                if (isTableVisible)
                  Column(
                    children: [
                      LoShoGrid(gridValues: loShoGridValues["grid"], loShoGridValues: loShoGridValues),
                      for (int i = 0; i < allTableValues.length; i++)
                        GeneratedTable(
                          tableValues: allTableValues[i],
                          tableIndex: i,
                          keyValue: i,
                        ),
                      // Display Proposed Mobile Number Analysis
                      if (_proposedMobileController.text.isNotEmpty)
                        ProposedMobileAnalysis(proposedNumber: _proposedMobileController.text),
                      const SizedBox(height: 10),

                      // Row to hold both buttons (Download PDF and Save)
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Download PDF Button
                          ElevatedButton.icon(
                            onPressed: () {
                              showPrintDialog(
                                context,
                                {
                                  'name': _nameController.text,
                                  'dob': _dobController.text,
                                  'gender': _gender,
                                  'mobile': _mobileController.text,
                                  'proposedMobile': _proposedMobileController.text,
                                },
                                allTableValues,
                                loShoGridValues,
                                _proposedMobileController,
                              );
                            },
                            icon: const Icon(Icons.picture_as_pdf),
                            label: const Text('Download PDF'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blueAccent,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
                              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(30)),
                              elevation: 5,
                            ),
                          ),
                        ],
                      ),
                    ],
                  )
              ],
            ),
          ),
        ),
      ),
    );
  }
}