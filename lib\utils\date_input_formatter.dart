import 'package:flutter/services.dart';

/// Custom Input Formatter for DD/MM/YYYY format
class DateInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    var text = newValue.text;
    if (text.length > 10) {
      return oldValue; // Limit input to 10 characters
    }
    text = text.replaceAll(RegExp(r'[^0-9]'), ''); // Keep only digits
    final buffer = StringBuffer();
    for (int i = 0; i < text.length; i++) {
      if (i == 2 || i == 4) buffer.write('/');
      buffer.write(text[i]);
    }
    return newValue.copyWith(
      text: buffer.toString(),
      selection: TextSelection.collapsed(offset: buffer.length),
    );
  }
}