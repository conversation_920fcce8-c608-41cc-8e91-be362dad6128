import 'package:flutter/material.dart';

class NumberAnalysisUtils {
  /// Calculates the sum of all digits and reduces to a single digit
  static int calculateSum(String number) {
    int sum = number.split('').map(int.parse).reduce((a, b) => a + b);
    return reduceToSingleDigit(sum);
  }

  /// Calculates the sum of the last five digits and reduces to a single digit
  static int calculateLastFiveSum(String number) {
    int sum = number.substring(5).split('').map(int.parse).reduce((a, b) => a + b);
    return reduceToSingleDigit(sum);
  }

  /// Finds repeated numbers in the given number string along with their counts
  static String findRepeatedNumbers(String number) {
    final counts = <String, int>{};

    // Count occurrences of each digit
    for (var digit in number.split('')) {
      counts[digit] = (counts[digit] ?? 0) + 1;
    }

    // Filter out digits that appear more than once and format output
    return counts.entries
        .where((e) => e.value > 1)
        .map((e) => '${e.key} [${e.value} times]')
        .join('\n');
  }

  /// Finds non-repetitive (unique) digits in the given number string
  static String findNonRepetitiveNumbers(String number) {
    final counts = <String, int>{};

    // Count occurrences of each digit
    for (var digit in number.split('')) {
      counts[digit] = (counts[digit] ?? 0) + 1;
    }

    // Filter digits that appear only once
    return counts.entries
        .where((e) => e.value == 1)
        .map((e) => e.key)
        .join(', ');
  }

  static Map<String, double> starDictionary = {
    "1.1": 4, "1.2": 3.5, "1.3": 3.5, "1.4": 3, "1.5": 4, "1.6": 3.5, "1.7": 3, "1.8": 0, "1.9": 5,
    "2.1": 3.5, "2.2": 2, "2.3": 2.5, "2.4": 1.5, "2.5": 3, "2.6": 2.5, "2.7": 2.5, "2.8": 0, "2.9": 1,
    "3.1": 3.5, "3.2": 2.5, "3.3": 3, "3.4": 2, "3.5": 3, "3.6": 0, "3.7": 4, "3.8": 2, "3.9": 4,
    "4.1": 3.5, "4.2": 2, "4.3": 2.5, "4.4": 1.5, "4.5": 3, "4.6": 3, "4.7": 4, "4.8": 1, "4.9": 1,
    "5.1": 4, "5.2": 3.5, "5.3": 3, "5.4": 3, "5.5": 4, "5.6": 4.5, "5.7": 3, "5.8": 3.5, "5.9": 0,
    "6.1": 3.5, "6.2": 2, "6.3": 0, "6.4": 3, "6.5": 4.5, "6.6": 4, "6.7": 3.5, "6.8": 3, "6.9": 3,
    "7.1": 3, "7.2": 3, "7.3": 3, "7.4": 4, "7.5": 3, "7.6": 4, "7.7": 1, "7.8": 1, "7.9": 1.5,
    "8.1": 1, "8.2": 0, "8.3": 2, "8.4": 1, "8.5": 3, "8.6": 3, "8.7": 2, "8.8": 1, "8.9": 1,
    "9.1": 4, "9.2": 1, "9.3": 2.5, "9.4": 1.5, "9.5": 3, "9.6": 2, "9.7": 1, "9.8": 2, "9.9": 2
  };

  static List<String> calculatePairingNumbers(String number) {
    // Remove all occurrences of '0' from the proposedNumber
    number = number.replaceAll('0', '');

    if (number.isEmpty) {
      return ["Invalid input"];
    }

    List<String> pairs = [];
    for (int i = 0; i < number.length - 1; i++) {
      String pair = "${number[i]}.${number[i + 1]}";
      double stars = starDictionary[pair] ?? 0; // Default to 0 if not found

      // Format stars: If whole number, show as integer; else, keep as float
      String starDisplay = stars % 1 == 0 ? stars.toInt().toString() : stars.toString();
      pairs.add("$pair - $starDisplay star");
    }
    return pairs;
  }

  /// Calculates the average of pairing numbers from a given number string.
  static double calculateAveragePairingNumber(String number) {
    List<double> pairingNumbers = [];

    for (int i = 0; i < number.length - 1; i++) {
      pairingNumbers.add(double.parse("${number[i]}${number[i + 1]}"));
    }

    // Return the average (avoid division by zero)
    return pairingNumbers.isNotEmpty
        ? pairingNumbers.reduce((a, b) => a + b) / pairingNumbers.length
        : 0;
  }


  /// Reduces a number to a single digit (digital root)
  static int reduceToSingleDigit(int num) {
    while (num > 9) {
      num = num.toString().split('').map(int.parse).reduce((a, b) => a + b);
    }
    return num;
  }
}

class ProposedMobileAnalysis extends StatelessWidget {
  final String proposedNumber;

  const ProposedMobileAnalysis({super.key, required this.proposedNumber});

  @override
  Widget build(BuildContext context) {
    if (proposedNumber.isEmpty) return const SizedBox.shrink();

    final sumAll = NumberAnalysisUtils.calculateSum(proposedNumber);
    final sumLastFive = NumberAnalysisUtils.calculateLastFiveSum(proposedNumber);
    final lastDigit = proposedNumber.substring(9);
    final repeatedNumbers = NumberAnalysisUtils.findRepeatedNumbers(proposedNumber);
    final nonrepeatNumbers = NumberAnalysisUtils.findNonRepetitiveNumbers(proposedNumber);
    final pairNumbers = NumberAnalysisUtils.calculatePairingNumbers(proposedNumber);
    final double averagePairingNumber = NumberAnalysisUtils.calculateAveragePairingNumber(proposedNumber);

    return Card(
      elevation: 5,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      color: Colors.deepPurple.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Proposed Mobile Number Analysis',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.deepPurple),
            ),
            const SizedBox(height: 10),
            _buildAnalysisRow('Sum of all digits (X):', sumAll.toString(), Colors.deepPurple),
            _buildAnalysisRow('Sum of last five digits (Y):', sumLastFive.toString(), Colors.blue),
            _buildAnalysisRow('Last digit (Z):', lastDigit, Colors.green),
            const Divider(color: Colors.deepPurple),
            _buildAnalysisRow('Repeated Numbers:', repeatedNumbers, Colors.redAccent),
            _buildAnalysisRow('Non-Repeated Numbers:', nonrepeatNumbers, Colors.orange),
            const Divider(color: Colors.deepPurple),
            _buildAnalysisRow('Pairing Numbers:', pairNumbers.join('\n'), Colors.indigo),
            SizedBox(height: 8), // Adds spacing
            _buildAnalysisRow('Average Pairing Numbers:', averagePairingNumber.toStringAsFixed(2), Colors.indigo),

          ],
        ),
      ),
    );
  }

  Widget _buildAnalysisRow(String label, String value, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: color,
            ),
          ),
          Flexible( // 👈 allows value text to wrap
            child: Text(
              value,
              softWrap: true,
              overflow: TextOverflow.visible,
              maxLines: null,
              textAlign: TextAlign.right, // keeps alignment to the right
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.brown,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
