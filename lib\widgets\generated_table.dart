import 'package:flutter/material.dart';
import '../helpers/math_helpers.dart';

class GeneratedTable extends StatelessWidget {
  final List<List<int>> tableValues;
  final int tableIndex;
  final int keyValue;

  const GeneratedTable({
    required this.tableValues,
    required this.tableIndex,
    required this.keyValue,
    super.key,
  });

  List<int> getRowSums() {
    return tableValues.map((row) {
      return digitRoot(row.fold(0, (sum, element) {
        return sum + digitRoot(element);
      }));
    }).toList();
  }

  List<int> getColumnSums() {
    int columnCount = tableValues[0].length;
    List<int> columnSums = List.filled(columnCount, 0);
    for (var row in tableValues) {
      for (int i = 0; i < columnCount; i++) {
        columnSums[i] += row[i];
      }
    }
    return columnSums.map((sum) => digitRoot(sum)).toList();
  }

  String getTableName() {
    Map<int, String> keyToNameMap = {
      1: "ಸೂರ್ಯ",
      2: "ಚಂದ್ರ",
      3: "ಗುರು",
      4: "ರಾಹು",
      5: "ಬುಧ",
      6: "ಶುಕ್ರ",
      7: "ಕೇತು",
      8: "ಶನಿ",
      9: "ಮಂಗಳ"
    };

    return tableIndex == 0
        ? 'Personal Yantra'
        : 'Yantra $keyValue: ${keyToNameMap[keyValue]} Yantra';
  }

  @override
  Widget build(BuildContext context) {
    List<int> rowSums = getRowSums();
    List<int> columnSums = getColumnSums();

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(color: Colors.black26, blurRadius: 10, offset: Offset(0, 4)),
        ],
        color: Colors.white,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            getTableName(),
            style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: Colors.deepPurple),
          ),
          const SizedBox(height: 16),
          Table(
            border: TableBorder.all(color: Colors.green, width: 1),
            children: [
              for (var row in tableValues)
                TableRow(
                  decoration: const BoxDecoration(color: Colors.white),
                  children: row.map((cell) {
                    return Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Center(
                        child: Text(
                          '$cell',
                          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.red),
                        ),
                      ),
                    );
                  }).toList(),
                ),
            ],
          ),
          const SizedBox(height: 12),
          // Row Sums Section (Compact Version)
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              const Text(
                'Row Sums: ',
                style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
              ),
              for (int i = 0; i < rowSums.length; i++)
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 4.0),
                  padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 6.0),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade100,
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(color: Colors.blue.shade200, blurRadius: 4, offset: Offset(0, 2)),
                    ],
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.check_circle_outline, color: Colors.blue.shade700, size: 12),
                      const SizedBox(width: 4),
                      Text('${rowSums[i]}', style: TextStyle(fontSize: 14, color: Colors.blue.shade700)),
                    ],
                  ),
                ),
            ],
          ),
          const SizedBox(height: 8),
          // Column Sums Section (Compact Version)
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              const Text(
                'Column Sums: ',
                style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
              ),
              for (int i = 0; i < columnSums.length; i++)
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 4.0),
                  padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 6.0),
                  decoration: BoxDecoration(
                    color: Colors.green.shade100,
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(color: Colors.green.shade200, blurRadius: 4, offset: Offset(0, 2)),
                    ],
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.check_circle_outline, color: Colors.green.shade700, size: 12),
                      const SizedBox(width: 4),
                      Text('${columnSums[i]}', style: TextStyle(fontSize: 14, color: Colors.green.shade700)),
                    ],
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }
}
