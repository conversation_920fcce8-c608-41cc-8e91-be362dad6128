import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:open_file/open_file.dart';
import 'package:permission_handler/permission_handler.dart';
import '../helpers/math_helpers.dart';
import '../widgets/proposed_mobile_analysis.dart';
import 'dart:developer';

Future<void> requestStoragePermission() async {
  if (await Permission.storage.request().isGranted) {
    log("Storage permission granted!", name: "Permission");
  } else {
    log("Storage permission denied!", name: "Permission");
  }
}


void showPrintDialog(BuildContext context, Map<String, dynamic> userData,
    List<List<dynamic>> allTableValues,
    Map<String, dynamic> loShoGridValues, TextEditingController proposedMobileController) {
  bool includeLoShoGrid = false;
  bool includeGeneratedTable = false;
  bool includeProposedAnalysis = false;
  bool selectAll = false;

  void updateSelectAllState() {
    selectAll = includeLoShoGrid && includeGeneratedTable && includeProposedAnalysis;
  }

  showDialog(
    context: context,
    builder: (BuildContext dialogContext) {
      return AlertDialog(
        title: const Text('Select Sections to Include in PDF'),
        content: StatefulBuilder(
          builder: (context, setState) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CheckboxListTile(
                  title: const Text('LoShoGrid'),
                  value: includeLoShoGrid,
                  onChanged: (value) => setState(() {
                    includeLoShoGrid = value!;
                    updateSelectAllState();
                  }),
                ),
                CheckboxListTile(
                  title: const Text('Generated Table'),
                  value: includeGeneratedTable,
                  onChanged: (value) => setState(() {
                    includeGeneratedTable = value!;
                    updateSelectAllState();
                  }),
                ),
                CheckboxListTile(
                  title: const Text('Proposed Mobile Analysis'),
                  value: includeProposedAnalysis,
                  onChanged: (value) => setState(() {
                    includeProposedAnalysis = value!;
                    updateSelectAllState();
                  }),
                ),
                CheckboxListTile(
                  title: const Text('All of them'),
                  value: selectAll,
                  onChanged: (value) {
                    setState(() {
                      selectAll = value!;
                      includeLoShoGrid = value;
                      includeGeneratedTable = value;
                      includeProposedAnalysis = value;
                    });
                  },
                ),
              ],
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(dialogContext),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.redAccent, // Button background
              foregroundColor: Colors.white,  // Text color
            ),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(dialogContext);
              generatePDF(
                context, // Pass context here!
                userData,
                allTableValues,
                loShoGridValues,
                proposedMobileController,
                includeLoShoGrid,
                includeGeneratedTable,
                includeProposedAnalysis,
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.indigo, // Button background
              foregroundColor: Colors.white,  // Text color
            ),
            child: const Text('Download PDF'),
          ),
        ],
      );
    },
  );
}

pw.TableRow _buildCell(String label, String value) {
  return pw.TableRow(
    children: [
      pw.Padding(
        padding: const pw.EdgeInsets.all(8),
        child: pw.Text(
          label,
          style: pw.TextStyle(
            fontWeight: pw.FontWeight.bold,
            fontSize: 14,
          ),
        ),
      ),
      pw.Padding(
        padding: const pw.EdgeInsets.all(8),
        child: pw.Text(
          value.isNotEmpty ? value : "-",
          style: pw.TextStyle(fontSize: 14),
        ),
      ),
    ],
  );
}

void generatePDF(
    BuildContext context, // Pass context here
    Map<String, dynamic> userData,
    List<List<dynamic>> allTableValues,
    Map<String, dynamic> loShoGridValues,
    TextEditingController proposedMobileController,
    bool includeLoShoGrid,
    bool includeGeneratedTable,
    bool includeProposedAnalysis,
    ) async {
  final pdf = pw.Document();

  List<String> tableNames = [
    "Personal Yantra",  // Table 1
    "Yantra 1: Sun",   // Table 2
    "Yantra 2: Moon",    // Table 3
    "Yantra 3: Jupiter",
    "Yantra 4: Rahu",
    "Yantra 5: Budha",
    "Yantra 6: Shukra",
    "Yantra 7: Ketu",
    "Yantra 8: Shani",
    "Yantra 9: Mangala",  // Table 10
  ];

  Future<Uint8List> loadLogo() async {
    final ByteData data = await rootBundle.load('assets/Numerology.jpg');
    return data.buffer.asUint8List();
  }
  final Uint8List logoImage = await loadLogo(); // Load the logo

  pdf.addPage(
    pw.MultiPage(
      pageFormat: PdfPageFormat.a4,
      footer: (pw.Context context) {
        return pw.Container(
          alignment: pw.Alignment.center,
          margin: const pw.EdgeInsets.only(top: 10), // Adjust as needed
          padding: const pw.EdgeInsets.only(top: 5),
          child: pw.Text(
            '© All Rights Reserved | Bharathi L.B - +91 7676274807',
            style: pw.TextStyle(
              fontSize: 12,
              color: PdfColors.grey,
            ),
          ),
        );
      },
      build: (pw.Context context) => [
        // **Header Section**
        pw.Container(
          width: double.infinity,
          padding: const pw.EdgeInsets.symmetric(horizontal: 0),
          color: PdfColor(0, 0, 0.2), // Navy Blue
          height: 80,
          child: pw.Row(
            crossAxisAlignment: pw.CrossAxisAlignment.stretch,
            children: [
              pw.Container(
                width: 70,
                decoration: const pw.BoxDecoration(
                  color: PdfColors.white,
                ),
                child: pw.Image(
                  pw.MemoryImage(logoImage),
                  fit: pw.BoxFit.cover,
                ),
              ),
              pw.SizedBox(width: 15),
              pw.Expanded(
                child: pw.Column(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceEvenly,
                  crossAxisAlignment: pw.CrossAxisAlignment.center,
                  children: [
                    pw.Text(
                      'Numerology'.toUpperCase(),
                      style: pw.TextStyle(
                        fontSize: 20,
                        fontWeight: pw.FontWeight.bold,
                        color: PdfColors.white,
                      ),
                    ),
                    pw.Text(
                      'By: Bharathi L.B',
                      style: pw.TextStyle(
                        fontSize: 16,
                        fontWeight: pw.FontWeight.bold,
                        color: PdfColors.white,
                      ),
                    ),
                    pw.Text(
                      'Mob: +91 7676274807',
                      style: pw.TextStyle(
                        fontSize: 16,
                        fontWeight: pw.FontWeight.normal,
                        color: PdfColors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        pw.SizedBox(height: 20),

        // **User Details**
        pw.Text(
          "User Details",
          style: pw.TextStyle(
            fontSize: 20,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.deepPurple,
          ),
        ),
        pw.SizedBox(height: 10),
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.grey),
          columnWidths: {
            0: const pw.FlexColumnWidth(1),
            1: const pw.FlexColumnWidth(1),
          },
          children: [
            _buildCell("Name", userData['name']),
            _buildCell("DOB", userData['dob']),
            _buildCell("Gender", userData['gender']),
            _buildCell("Mobile", userData['mobile']),
            _buildCell("Proposed Mobile", userData['proposedMobile']),
          ],
        ),
        pw.SizedBox(height: 20),

        // **LoSho Grid Section**
        if (includeLoShoGrid) ...[
          pw.Text(
            'LoSho Grid Section',
            style: pw.TextStyle(
              fontSize: 20,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.deepPurple,
            ),
          ),
          pw.SizedBox(height: 10),
          buildLoShoGridPdf(loShoGridValues),
          pw.SizedBox(height: 120),
        ],

        // **Generated Tables Section**
        if (includeGeneratedTable) ...[
          pw.Text(
            'Generated Tables',
            style: pw.TextStyle(fontSize: 20, fontWeight: pw.FontWeight.bold, color: PdfColors.deepPurple),
          ),
          pw.SizedBox(height: 30),

          // **Process Tables in Pairs (Side by Side)**
          for (int i = 0; i < allTableValues.length; i += 2) ...[
            pw.Row(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              mainAxisAlignment: pw.MainAxisAlignment.spaceEvenly, // Distributes evenly
              children: [
                // Ensure the list is correctly cast before passing it
                pw.Expanded(
                  child: buildTableWithSums(
                    (allTableValues[i]).map((e) => (e as List).map((v) => v as int).toList()).toList(),
                    tableNames[i],
                  ),
                ),
                if (i + 1 < allTableValues.length) pw.SizedBox(width: 20),
                if (i + 1 < allTableValues.length)
                  pw.Expanded(
                    child: buildTableWithSums(
                      (allTableValues[i + 1]).map((e) => (e as List).map((v) => v as int).toList()).toList(),
                      tableNames[i + 1],
                    ),
                  ),

              ],
            ),
            pw.SizedBox(height: 20), // Space between table rows
          ],
        ],

        // **Proposed Mobile Analysis**
        if (includeProposedAnalysis && proposedMobileController.text.isNotEmpty) ...[
          pw.Container(
            padding: pw.EdgeInsets.symmetric(vertical: 8),
            decoration: pw.BoxDecoration(
              border: pw.Border(bottom: pw.BorderSide(color: PdfColors.blue, width: 1.5)),
            ),
            child: pw.Text(
              'Proposed Mobile Analysis',
              style: pw.TextStyle(fontSize: 22, fontWeight: pw.FontWeight.bold, color: PdfColors.deepPurple),
            ),
          ),

          pw.SizedBox(height: 20),

          // **Display the Proposed Number**
          pw.Container(
            padding: pw.EdgeInsets.all(8),
            decoration: pw.BoxDecoration(
              borderRadius: pw.BorderRadius.circular(5),
              border: pw.Border.all(color: PdfColors.blue, width: 1),
              color: PdfColors.grey200,
            ),
            child: pw.Text(
              'Proposed Number: ${proposedMobileController.text}',
              style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold, color: PdfColors.black),
            ),
          ),

          pw.SizedBox(height: 12),

          // **Perform Analysis**
              () {
            String proposedNumber = proposedMobileController.text;

            int totalSum = NumberAnalysisUtils.calculateSum(proposedNumber);
            int lastFiveSum = NumberAnalysisUtils.calculateLastFiveSum(proposedNumber);
            String repeatedNumbers = NumberAnalysisUtils.findRepeatedNumbers(proposedNumber);
            String nonRepetitiveNumbers = NumberAnalysisUtils.findNonRepetitiveNumbers(proposedNumber);
            List<String> pairingNumbers = NumberAnalysisUtils.calculatePairingNumbers(proposedNumber);
            double averagePairing = NumberAnalysisUtils.calculateAveragePairingNumber(proposedNumber);

            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Container(
                  padding: pw.EdgeInsets.all(6),
                  decoration: pw.BoxDecoration(
                    borderRadius: pw.BorderRadius.circular(5),
                    border: pw.Border.all(color: PdfColors.grey, width: 1),
                    color: PdfColors.grey300,
                  ),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text("Single-Digit Sum: $totalSum", style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold)),
                      pw.SizedBox(height: 4),
                      pw.Text("Last 5 Digits Sum: $lastFiveSum", style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold)),
                      pw.SizedBox(height: 4),
                      pw.Text("Repeated Digits:\n${repeatedNumbers.isEmpty ? 'None' : repeatedNumbers}", style: pw.TextStyle(fontSize: 14)),
                      pw.SizedBox(height: 4),
                      pw.Text("Unique Digits: ${nonRepetitiveNumbers.isEmpty ? 'None' : nonRepetitiveNumbers}", style: pw.TextStyle(fontSize: 14)),
                    ],
                  ),
                ),

                pw.SizedBox(height: 12),

                // **Pairing Numbers in a Boxed Format**
                pw.Text("Pairing Numbers:", style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold, color: PdfColors.blue)),

                pw.Container(
                  padding: pw.EdgeInsets.all(8),
                  margin: pw.EdgeInsets.only(top: 6),
                  decoration: pw.BoxDecoration(
                    borderRadius: pw.BorderRadius.circular(5),
                    border: pw.Border.all(color: PdfColors.green, width: 1),
                  ),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: pairingNumbers.map((pair) => pw.Text(pair, style: pw.TextStyle(fontSize: 12, color: PdfColors.green900))).toList(),
                  ),
                ),

                pw.SizedBox(height: 10),

                // **Average Pairing Number**
                pw.Container(
                  padding: pw.EdgeInsets.all(6),
                  decoration: pw.BoxDecoration(
                    borderRadius: pw.BorderRadius.circular(5),
                    border: pw.Border.all(color: PdfColors.orange, width: 1),
                    color: PdfColors.orange50,
                  ),
                  child: pw.Text(
                    "Average Pairing Number: ${averagePairing.toStringAsFixed(2)}",
                    style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold, color: PdfColors.orange900),
                  ),
                ),
              ],
            );
          }(),
        ],


        pw.SizedBox(height: 20),

        // **Footer Section**
      ],
    ),
  );

  final pdfBytes = await pdf.save();
  // Only call saveAndOpenPDF if the widget is still mounted
  if (context.mounted) {
    saveAndOpenPDF(context, pdfBytes, "${userData['name']} - Numerology.pdf");
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('PDF generated and saved!')),
    );
  }
}
void saveAndOpenPDF(BuildContext context, Uint8List pdfBytes, String fileName) async {
  await requestStoragePermission();  // Request permission before saving

  try {
    final dir = await getApplicationDocumentsDirectory();
    final filePath = '${dir.path}/$fileName';
    final file = File(filePath);

    await file.writeAsBytes(pdfBytes);
    final result = await OpenFile.open(filePath);

    if (result.type == ResultType.noAppToOpen) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('No app found to open the PDF.')),
        );
      }
    }
  } catch (e) {
    if (context.mounted) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error generating PDF: $e')),
        );
      });
    }
  }
}

pw.Widget buildLoShoGridPdf(Map<String, dynamic> loShoGridValues) {
  List<List<int>> gridValues = (loShoGridValues['grid'] as List)
      .map((row) => (row as List).map((e) => e as int).toList())
      .toList();
  List<int> unrepeatedNumbers = loShoGridValues['unrepeatedNumbers'] ?? [];
  int driver = loShoGridValues['driver'] ?? 1;
  int conductor = loShoGridValues['conductor'] ?? 1;
  int kua = loShoGridValues['kua'];
  String lotteryNumber = loShoGridValues['lotteryNumber'];

  Map<String, String> topLabels = {
    '0,0': 'Rahu',
    '0,1': 'Mars',
    '0,2': 'Moon',
    '1,0': 'Jupiter',
    '1,1': 'Mercury',
    '1,2': 'Ketu',
    '2,0': 'Saturn',
    '2,1': 'Sun',
    '2,2': 'Venus',
  };

  Map<String, PdfColor> planetColors = {
    'Sun': PdfColor(1.0, 0.93, 0.85),       // Lighter light orange
    'Moon': PdfColors.white,
    'Mars': PdfColor(1.0, 0.75, 0.75),      // Lighter red
    'Mercury': PdfColor(0.9, 1.0, 0.9),     // Lighter green
    'Jupiter': PdfColor(1.0, 1.0, 0.85),    // Lighter yellow
    'Venus': PdfColor(0.93, 0.88, 1.0),     // Lighter purple
    'Saturn': PdfColor(0.85, 0.93, 1.0),    // Lighter blue
    'Rahu': PdfColor(0.93, 0.93, 0.93),     // Lighter grey
    'Ketu': PdfColor(0.9, 0.8, 0.75),       // Lighter brown
  };

  // Compatibility Chart (9 rows, 5 columns)
  List<List<String>> compatibilityChart = [
    ["1", "Sun", "9,2,5,3,1,6", "8", "4,7"],
    ["2", "Moon", "1,5,3,2", "8,9,4", "6,7"],
    ["3", "Jupiter", "1,5,3,2", "6", "4,8,9,7"],
    ["4", "Rahu", "7,1,5,6", "2,9,4,8", "3"],
    ["5", "Budha", "1,2,3,5,6", "", "7,8,9,4"],
    ["6", "Shukra", "1,5,7,6", "3", "4,9,8,2,7"],
    ["7", "Ketu", "4,6,1,3,5", "", "8,2,9,7"],
    ["8", "Shani", "3,5,7,6", "1,2,4,8", "9"],
    ["9", "Mangala", "1,5,3", "4,2", "6,7,9,8"],
  ];

  // Ensure driver and conductor are within valid range (1-9)
  List<String> driverRow = (driver >= 1 && driver <= 9)
      ? compatibilityChart[driver - 1]
      : ["-", "-", "-", "-", "-"]; // Default row if out of range

  List<String> conductorRow = (conductor >= 1 && conductor <= 9)
      ? compatibilityChart[conductor - 1]
      : ["-", "-", "-", "-", "-"]; // Default row if out of range

  Set<int> driverFriendly = driverRow[2].split(',').where((s) => s.isNotEmpty).map(int.parse).toSet();
  Set<int> conductorFriendly = conductorRow[2].split(',').where((s) => s.isNotEmpty).map(int.parse).toSet();

  Set<int> driverUnfriendly = driverRow[3].split(',').where((s) => s.isNotEmpty).map(int.parse).toSet();
  Set<int> conductorUnfriendly = conductorRow[3].split(',').where((s) => s.isNotEmpty).map(int.parse).toSet();

  Set<int> driverNeutral = driverRow[4].split(',').where((s) => s.isNotEmpty).map(int.parse).toSet();
  Set<int> conductorNeutral = conductorRow[4].split(',').where((s) => s.isNotEmpty).map(int.parse).toSet();


  // Lucky Number Logic
  Set<int> luckyNumbers = driverFriendly.intersection(conductorFriendly); //Common Friendly Numbers

  //Include additional numbers based on neutral logic
  Set<int> additionalLuckyNumbers = {};

  // If a driver-friendly number is in conductor-neutral, keep it
  for (int num in driverFriendly) {
    if (conductorNeutral.contains(num)) {
      additionalLuckyNumbers.add(num);
    }
  }

  // If a driver-friendly number is in driver-neutral, keep it
  for (int num in driverFriendly) {
    if (driverNeutral.contains(num)) {
      additionalLuckyNumbers.add(num);
    }
  }

  // If a conductor-friendly number is in driver-neutral, keep it
  for (int num in conductorFriendly) {
    if (driverNeutral.contains(num)) {
      additionalLuckyNumbers.add(num);
    }
  }

  // If a conductor-friendly number is in conductor-neutral, keep it
  for (int num in conductorFriendly) {
    if (conductorNeutral.contains(num)) {
      additionalLuckyNumbers.add(num);
    }
  }

  // Combine the additional lucky numbers with the original lucky numbers
  luckyNumbers.addAll(additionalLuckyNumbers);
  luckyNumbers.removeAll({4,8});
  luckyNumbers.removeWhere((n) => driverUnfriendly.contains(n) || conductorUnfriendly.contains(n)); // Remove Unfriendly

  // Unlucky Number Logic
  Set<int> unluckyNumbers = driverUnfriendly.union(conductorUnfriendly);
  unluckyNumbers.addAll({4, 8}); // Always include 4 and 8

  // Neutral Number Logic
  Set<int> neutralNumbers = driverNeutral.intersection(conductorNeutral);
  if (luckyNumbers.contains(7)) {
    luckyNumbers.remove(7); // Remove 7 Always
    neutralNumbers.add(7);  // Move it to Neutral Numbers
  }
  neutralNumbers.removeAll({4, 8}); // Remove 4 and 8 since they are in Unlucky


  pw.Widget buildStyledRow(String value, PdfColor color) {
    return pw.Padding(
      padding: const pw.EdgeInsets.all(5.0),
      child: pw.Center(
        child: pw.Text(
          value,
          style: pw.TextStyle(
            fontSize: 14,
            fontWeight: pw.FontWeight.bold,
            color: color,
          ),
        ),
      ),
    );
  }


  return pw.Column(
    crossAxisAlignment: pw.CrossAxisAlignment.start,
    children: [
      // 🟩 Grid Table
      pw.Table(
        border: pw.TableBorder.all(color: PdfColors.green, width: 1),
        columnWidths: {
          for (var i = 0; i < gridValues[0].length; i++) i: const pw.FlexColumnWidth(1),
        },
        children: [
          for (int rowIndex = 0; rowIndex < gridValues.length; rowIndex++)
            pw.TableRow(
              children: [
                for (int colIndex = 0; colIndex < gridValues[rowIndex].length; colIndex++)
                  pw.Container(
                    height: 90,
                    color: planetColors[topLabels['$rowIndex,$colIndex'] ?? ''] ?? PdfColors.white,
                    padding: const pw.EdgeInsets.all(12),
                    alignment: pw.Alignment.center,
                    child: pw.Column(
                      mainAxisSize: pw.MainAxisSize.min,
                      children: [
                        // Top label
                        pw.Text(
                          topLabels['$rowIndex,$colIndex'] ?? '',
                          style: pw.TextStyle(
                            fontSize: 14,
                            fontWeight: pw.FontWeight.normal,
                            color: PdfColors.teal,
                          ),
                        ),
                        pw.SizedBox(height: 6),
                        // Number value
                        pw.Text(
                          gridValues[rowIndex][colIndex] == 0
                              ? ""
                              : '${gridValues[rowIndex][colIndex]}',
                          style: pw.TextStyle(
                            fontSize: 28,
                            fontWeight: pw.FontWeight.bold,
                            color: PdfColors.red,
                          ),
                        ),
                        // Underline if unrepeated
                        if (unrepeatedNumbers.contains(gridValues[rowIndex][colIndex]))
                          pw.Container(
                            margin: const pw.EdgeInsets.only(top: 5),
                            width: 30,
                            height: 3,
                            color: PdfColors.green,
                          ),
                      ],
                    ),
                  ),
              ],
            ),
        ],
      ),
      pw.SizedBox(height: 70),
      pw.Text(
        'Driver, Conductor, Kua and Lottery numbers based on Lo Sho Grid',
        style: pw.TextStyle(
          fontSize: 16,
          color: PdfColors.deepPurple,
        ),
      ),
      pw.SizedBox(height: 20),
      // 🟩 Driver, Conductor, Lucky Numbers
      pw.Table(
        border: pw.TableBorder.all(color: PdfColors.grey),
        columnWidths: {
          0: const pw.FlexColumnWidth(1), // Label Column
          1: const pw.FlexColumnWidth(1), // Data Column
        },
        children: [
          _buildCell("Driver Number", driver.toString()),
          _buildCell("Conductor Number", conductor.toString()),
          _buildCell("Kua Number", kua.toString()),
          _buildCell("Lottery Number", lotteryNumber.toString())
        ],
      ),
      pw.SizedBox(height: 30),
      pw.Text(
        'Compatibility Chart based on Driver and Conductor',
        style: pw.TextStyle(
          fontSize: 16,
          color: PdfColors.deepPurple,
        ),
      ),
      pw.SizedBox(height: 20),
      pw.Table(
        border: pw.TableBorder.all(color: PdfColors.green, width: 1),
        children: [
          // Header Row
          pw.TableRow(
            decoration: pw.BoxDecoration(color: PdfColors.grey),
            children: [
              buildStyledRow("Category", PdfColors.brown),
              buildStyledRow("Driver (D)", PdfColors.brown),
              buildStyledRow("Conductor (C)", PdfColors.brown),
            ],
          ),
          // Data Rows
          pw.TableRow(
            children: [
              buildStyledRow("Number", PdfColors.brown),
              buildStyledRow(driverRow[0], PdfColors.pink),
              buildStyledRow(conductorRow[0], PdfColors.blue),
            ],
          ),
          pw.TableRow(
            children: [
              buildStyledRow("Body", PdfColors.brown),
              buildStyledRow(driverRow[1], PdfColors.pink),
              buildStyledRow(conductorRow[1], PdfColors.blue),
            ],
          ),
          pw.TableRow(
            children: [
              buildStyledRow("Friendly", PdfColors.brown),
              buildStyledRow(driverRow[2], PdfColors.pink),
              buildStyledRow(conductorRow[2], PdfColors.blue),
            ],
          ),
          pw.TableRow(
            children: [
              buildStyledRow("Unfriendly", PdfColors.brown),
              buildStyledRow(driverRow[3], PdfColors.pink),
              buildStyledRow(conductorRow[3], PdfColors.blue),
            ],
          ),
          pw.TableRow(
            children: [
              buildStyledRow("Neutral", PdfColors.brown),
              buildStyledRow(driverRow[4], PdfColors.pink),
              buildStyledRow(conductorRow[4], PdfColors.blue),
            ],
          ),
        ],
      ),

      pw.SizedBox(height: 30),
      pw.Text(
        'Lucky, Unlucky and Neutral Numbers based on Compatibility Chart ',
        style: pw.TextStyle(
          fontSize: 16,
          color: PdfColors.deepPurple,
        ),
      ),
      pw.SizedBox(height: 20),
      // Lucky, Unlucky, Neutral Numbers
      pw.Table(
        border: pw.TableBorder.all(color: PdfColors.grey),
        columnWidths: {
          0: const pw.FlexColumnWidth(1), // Label Column
          1: const pw.FlexColumnWidth(1), // Data Column
        },
        children: [
          _buildCell("Lucky Numbers", luckyNumbers.join(", ")),
          _buildCell("Unlucky Numbers", unluckyNumbers.join(", ")),
          _buildCell("Neutral Numbers", neutralNumbers.join(", "))
        ],
      ),

    ],
  );
}


pw.Widget buildTableWithSums(List<List<int>> table, String tableName) {
  // **Calculate Single-Digit Row Sums**
  List<int> rowSums = [
    for (var row in table) digitRoot(row.reduce((a, b) => a + b))
  ];

  // **Calculate Single-Digit Column Sums**
  List<int> columnSums = List.generate(4, (col) => digitRoot(table.fold(0, (sum, row) => sum + row[col])));

  return pw.Column(
    crossAxisAlignment: pw.CrossAxisAlignment.start,
    children: [
      // **Table Heading**
      pw.Text(
        tableName,
        style: pw.TextStyle(
          fontSize: 16,
          fontWeight: pw.FontWeight.bold,
          color: PdfColors.black,
        ),
      ),
      pw.SizedBox(height: 8),

      // **Main Table with Row Sums**
      pw.Row(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          // **Main Table**
          pw.Column(
            children: [
              for (int rowIndex = 0; rowIndex < 4; rowIndex++)
                pw.Row(
                  children: [
                    for (int colIndex = 0; colIndex < 4; colIndex++)
                      pw.Container(
                        width: 40,
                        height: 40,
                        alignment: pw.Alignment.center,
                        decoration: pw.BoxDecoration(
                          border: pw.Border.all(color: PdfColors.green, width: 2),
                        ),
                        child: pw.Text(
                          table[rowIndex][colIndex].toString(),
                          style: pw.TextStyle(
                            fontSize: 14,
                            fontWeight: pw.FontWeight.bold,
                            color: PdfColors.red,
                          ),
                        ),
                      ),
                    // **Row Sum (Blue, Right Side)**
                    pw.SizedBox(width: 8),
                    pw.Container(
                      width: 30,
                      child: pw.Text(
                        rowSums[rowIndex].toString(),
                        style: pw.TextStyle(
                          fontSize: 16,
                          fontWeight: pw.FontWeight.bold,
                          color: PdfColors.blue,
                        ),
                      ),
                    ),
                  ],
                ),
            ],
          ),
        ],
      ),

      pw.SizedBox(height: 8),

      // **Column Sums (Below the Table)**
      pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.start,
        children: [
          pw.SizedBox(width: 2), // Adjust alignment
          for (int colIndex = 0; colIndex < 4; colIndex++)
            pw.Container(
              width: 40,
              alignment: pw.Alignment.center,
              child: pw.Text(
                columnSums[colIndex].toString(),
                style: pw.TextStyle(
                  fontSize: 16,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.blue,
                ),
              ),
            ),
        ],
      ),
    ],
  );
}



