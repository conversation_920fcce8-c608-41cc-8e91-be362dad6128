import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/user_model.dart';

class UserService {
  UserService._();
  static final UserService instance = UserService._();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _usersCollection = 'users';

  /// Create a new user profile in Firestore
  Future<void> createUserProfile({
    required String uid,
    required String email,
    required String fullName,
    required String gender,
    required int age,
    required String phoneNumber,
    required bool emailVerified,
  }) async {
    try {
      final userModel = UserModel(
        uid: uid,
        email: email,
        fullName: fullName,
        gender: gender,
        age: age,
        phoneNumber: phoneNumber,
        emailVerified: emailVerified,
        createdAt: DateTime.now(),
      );

      await _firestore
          .collection(_usersCollection)
          .doc(uid)
          .set(userModel.toMap());

      debugPrint('User profile created successfully for UID: $uid');
    } catch (e) {
      debugPrint('Error creating user profile: $e');
      rethrow;
    }
  }

  /// Get user profile by UID
  Future<UserModel?> getUserProfile(String uid) async {
    try {
      final doc = await _firestore
          .collection(_usersCollection)
          .doc(uid)
          .get();

      if (doc.exists) {
        return UserModel.fromSnapshot(doc);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting user profile: $e');
      rethrow;
    }
  }

  /// Update user profile
  Future<void> updateUserProfile({
    required String uid,
    String? fullName,
    String? gender,
    int? age,
    String? phoneNumber,
    bool? emailVerified,
  }) async {
    try {
      final Map<String, dynamic> updateData = {
        'updatedAt': DateTime.now().toIso8601String(),
      };

      if (fullName != null) updateData['fullName'] = fullName;
      if (gender != null) updateData['gender'] = gender;
      if (age != null) updateData['age'] = age;
      if (phoneNumber != null) updateData['phoneNumber'] = phoneNumber;
      if (emailVerified != null) updateData['emailVerified'] = emailVerified;

      await _firestore
          .collection(_usersCollection)
          .doc(uid)
          .update(updateData);

      debugPrint('User profile updated successfully for UID: $uid');
    } catch (e) {
      debugPrint('Error updating user profile: $e');
      rethrow;
    }
  }

  /// Update email verification status
  Future<void> updateEmailVerificationStatus(String uid, bool isVerified) async {
    try {
      await _firestore
          .collection(_usersCollection)
          .doc(uid)
          .update({
        'emailVerified': isVerified,
        'updatedAt': DateTime.now().toIso8601String(),
      });

      debugPrint('Email verification status updated for UID: $uid');
    } catch (e) {
      debugPrint('Error updating email verification status: $e');
      rethrow;
    }
  }

  /// Delete user profile
  Future<void> deleteUserProfile(String uid) async {
    try {
      await _firestore
          .collection(_usersCollection)
          .doc(uid)
          .delete();

      debugPrint('User profile deleted successfully for UID: $uid');
    } catch (e) {
      debugPrint('Error deleting user profile: $e');
      rethrow;
    }
  }

  /// Check if user profile exists
  Future<bool> userProfileExists(String uid) async {
    try {
      final doc = await _firestore
          .collection(_usersCollection)
          .doc(uid)
          .get();

      return doc.exists;
    } catch (e) {
      debugPrint('Error checking if user profile exists: $e');
      return false;
    }
  }

  /// Stream user profile changes
  Stream<UserModel?> streamUserProfile(String uid) {
    return _firestore
        .collection(_usersCollection)
        .doc(uid)
        .snapshots()
        .map((snapshot) {
      if (snapshot.exists) {
        return UserModel.fromSnapshot(snapshot);
      }
      return null;
    });
  }

  /// Get all users (admin function - use with caution)
  Future<List<UserModel>> getAllUsers() async {
    try {
      final querySnapshot = await _firestore
          .collection(_usersCollection)
          .get();

      return querySnapshot.docs
          .map((doc) => UserModel.fromSnapshot(doc))
          .toList();
    } catch (e) {
      debugPrint('Error getting all users: $e');
      rethrow;
    }
  }
}
