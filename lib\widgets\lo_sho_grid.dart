import 'package:flutter/material.dart';

class LoShoGrid extends StatelessWidget {
  final List<List<int>> gridValues;
  final Map<String, dynamic> loShoGridValues;

  const LoShoGrid({
    required this.gridValues,
    required this.loShoGridValues,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    List<int> unrepeatedNumbers = loShoGridValues["unrepeatedNumbers"] ?? [];
    int driver = loShoGridValues["driver"] ?? 1; // Default to 1 if not found
    int conductor = loShoGridValues["conductor"] ?? 1; // Default to 1 if not found

    Map<String, String> topLabels = {
      '0,0': 'Rahu',
      '0,1': 'Mars',
      '0,2': 'Moon',
      '1,0': 'Jupiter',
      '1,1': 'Mercury',
      '1,2': 'Ketu',
      '2,0': 'Saturn',
      '2,1': 'Sun',
      '2,2': 'Venus',
    };

    Map<String, Color> planetColors = {
      'Sun': Colors.orange.shade100,
      'Moon': Colors.white,
      'Mars': Colors.red.shade100,
      'Mercury': Colors.lightGreen.shade100,
      'Jupiter': Colors.yellow.shade50,
      'Venus': Colors.deepPurple.shade50,
      'Saturn': Colors.blue.shade50,
      'Rahu': Colors.grey.shade200,
      'Ketu': Colors.brown.shade100,
    };
    // Compatibility Chart (9 rows, 5 columns)
    List<List<String>> compatibilityChart = [
      ["1", "ಸೂರ್ಯ", "9,2,5,3,1,6", "8", "4,7"],
      ["2", "ಚಂದ್ರ", "1,5,3,2", "8,9,4", "6,7"],
      ["3", "ಗುರು", "1,5,3,2", "6", "4,8,9,7"],
      ["4", "ರಾಹು", "7,1,5,6", "2,9,4,8", "3"],
      ["5", "ಬುಧ", "1,2,3,5,6", "", "7,8,9,4"],
      ["6", "ಶುಕ್ರ", "1,5,7,6", "3", "4,9,8,2,7"],
      ["7", "ಕೇತು", "4,6,1,3,5", "", "8,2,9,7"],
      ["8", "ಶನಿ", "3,5,7,6", "1,2,4,8", "9"],
      ["9", "ಮಂಗಳ", "1,5,3", "4,2", "6,7,9,8"],
    ];

    // Ensure driver and conductor are within valid range (1-9)
    List<String> driverRow = (driver >= 1 && driver <= 9)
        ? compatibilityChart[driver - 1]
        : ["-", "-", "-", "-", "-"]; // Default row if out of range

    List<String> conductorRow = (conductor >= 1 && conductor <= 9)
        ? compatibilityChart[conductor - 1]
        : ["-", "-", "-", "-", "-"]; // Default row if out of range

    Set<int> driverFriendly = driverRow[2].split(',').where((s) => s.isNotEmpty).map(int.parse).toSet();
    Set<int> conductorFriendly = conductorRow[2].split(',').where((s) => s.isNotEmpty).map(int.parse).toSet();

    Set<int> driverUnfriendly = driverRow[3].split(',').where((s) => s.isNotEmpty).map(int.parse).toSet();
    Set<int> conductorUnfriendly = conductorRow[3].split(',').where((s) => s.isNotEmpty).map(int.parse).toSet();

    Set<int> driverNeutral = driverRow[4].split(',').where((s) => s.isNotEmpty).map(int.parse).toSet();
    Set<int> conductorNeutral = conductorRow[4].split(',').where((s) => s.isNotEmpty).map(int.parse).toSet();


    // Lucky Number Logic
    Set<int> luckyNumbers = driverFriendly.intersection(conductorFriendly); //Common Friendly Numbers

    //Include additional numbers based on neutral logic
    Set<int> additionalLuckyNumbers = {};

    // If a driver-friendly number is in conductor-neutral, keep it
    for (int num in driverFriendly) {
      if (conductorNeutral.contains(num)) {
        additionalLuckyNumbers.add(num);
      }
    }

    // If a driver-friendly number is in driver-neutral, keep it
    for (int num in driverFriendly) {
      if (driverNeutral.contains(num)) {
        additionalLuckyNumbers.add(num);
      }
    }

    // If a conductor-friendly number is in driver-neutral, keep it
    for (int num in conductorFriendly) {
      if (driverNeutral.contains(num)) {
        additionalLuckyNumbers.add(num);
      }
    }

    // If a conductor-friendly number is in conductor-neutral, keep it
    for (int num in conductorFriendly) {
      if (conductorNeutral.contains(num)) {
        additionalLuckyNumbers.add(num);
      }
    }

    // Combine the additional lucky numbers with the original lucky numbers
    luckyNumbers.addAll(additionalLuckyNumbers);
    luckyNumbers.removeAll({4,8});
    luckyNumbers.removeWhere((n) => driverUnfriendly.contains(n) || conductorUnfriendly.contains(n)); // Remove Unfriendly

    // Unlucky Number Logic
    Set<int> unluckyNumbers = driverUnfriendly.union(conductorUnfriendly);
    unluckyNumbers.addAll({4, 8}); // Always include 4 and 8

    // Neutral Number Logic
    Set<int> neutralNumbers = driverNeutral.intersection(conductorNeutral);
    if (luckyNumbers.contains(7)) {
      luckyNumbers.remove(7); // Remove 7 Always
      neutralNumbers.add(7);  // Move it to Neutral Numbers
    }
    neutralNumbers.removeAll({4, 8}); // Remove 4 and 8 since they are in Unlucky

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(color: Colors.black26, blurRadius: 10, offset: Offset(0, 4)),
        ],
        color: Colors.white,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Chaldean Name Number: ${loShoGridValues["nameNumber"]}",
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.deepPurple,
            ),
          ),
          const SizedBox(height: 5),
          const Divider(color: Colors.deepPurple),
          const SizedBox(height: 5),
          const Text(
            'Lo Sho Grid',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: Colors.deepPurple),
          ),
          const SizedBox(height: 16),
          Table(
            border: TableBorder.all(color: Colors.green, width: 1),
            children: [
              for (int rowIndex = 0; rowIndex < gridValues.length; rowIndex++)
                TableRow(
                  decoration: const BoxDecoration(color: Colors.white),
                  children: [
                    for (int colIndex = 0; colIndex < gridValues[rowIndex].length; colIndex++)
                      Container(
                        height: 75,
                        color: planetColors[topLabels['$rowIndex,$colIndex']] ?? Colors.white, // cell color based on planet
                        padding: const EdgeInsets.all(8.0),
                        child: Center(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // Top Label
                              Text(
                                topLabels['$rowIndex,$colIndex'] ?? '',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.teal.shade300,
                                ),
                              ),
                              const SizedBox(height: 6),
                              // Number Text
                              Text(
                                gridValues[rowIndex][colIndex] == 0
                                    ? ""
                                    : '${gridValues[rowIndex][colIndex]}',
                                style: const TextStyle(
                                  fontSize: 22,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.red,
                                ),
                              ),
                              // Underline if unrepeated
                              if (unrepeatedNumbers.contains(gridValues[rowIndex][colIndex]))
                                Padding(
                                  padding: const EdgeInsets.only(top: 2),
                                  child: Container(
                                    width: 25,
                                    height: 3,
                                    color: Colors.green,
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                  ],
                ),
            ],
          ),
          const SizedBox(height: 10),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text("Driver Number: $driver",
                    style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.deepPurpleAccent)),
                Text("Conductor Number: ${loShoGridValues["conductor"]}",
                    style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.deepPurpleAccent)),
                Text("Kua Number: ${loShoGridValues["kua"]}",
                    style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.deepPurpleAccent)),
                Text("Lottery Number: ${loShoGridValues["lotteryNumber"]}",
                    style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.deepPurpleAccent)),
              ],
            ),
          ),
          const SizedBox(height: 10),
          Table(
            border: TableBorder.all(color: Colors.green, width: 1),
            children: [
              // Header Row (Now Only 3 Columns)
              TableRow(
                decoration: BoxDecoration(color: Colors.grey[300]), // Light grey background for headers
                children: [
                  _buildCell("Category", Colors.brown),
                  _buildCell("Driver (D)", Colors.brown),
                  _buildCell("Conductor (C)", Colors.brown),
                ],
              ),
              // Rows for Each Attribute
              TableRow(
                children: [
                  _buildCell("Number", Colors.brown.shade900),
                  _buildCell(driverRow[0], Colors.pink),
                  _buildCell(conductorRow[0], Colors.blue),
                ],
              ),
              TableRow(
                children: [
                  _buildCell("Body", Colors.brown.shade900),
                  _buildCell(driverRow[1], Colors.pink),
                  _buildCell(conductorRow[1], Colors.blue),
                ],
              ),
              TableRow(
                children: [
                  _buildCell("Friendly", Colors.brown.shade900),
                  _buildCell(driverRow[2], Colors.pink),
                  _buildCell(conductorRow[2], Colors.blue),
                ],
              ),
              TableRow(
                children: [
                  _buildCell("Unfriendly", Colors.brown.shade900),
                  _buildCell(driverRow[3], Colors.pink),
                  _buildCell(conductorRow[3], Colors.blue),
                ],
              ),
              TableRow(
                children: [
                  _buildCell("Neutral", Colors.brown.shade900),
                  _buildCell(driverRow[4], Colors.pink),
                  _buildCell(conductorRow[4], Colors.blue),
                ],
              ),
            ],
          ),

          const SizedBox(height: 16),
          // Lucky, Unlucky, and Neutral Numbers
          Text("Lucky Numbers: ${luckyNumbers.isEmpty ? 'None' : luckyNumbers.join(', ')}",
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.green)),
          Text("Unlucky Numbers: ${unluckyNumbers.isEmpty ? 'None' : unluckyNumbers.join(', ')}",
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.red)),
          Text("Neutral Numbers: ${neutralNumbers.isEmpty ? 'None' : neutralNumbers.join(', ')}",
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.orange)),
        ],
      ),
    );
  }
  // Function to build table cell with text style
  Widget _buildCell(String value, Color color) {
    return Padding(
      padding: const EdgeInsets.all(5.0),
      child: Center(
        child: Text(
          value,
          style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold, color: color),
        ),
      ),
    );
  }
}
