import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:google_sign_in/google_sign_in.dart';
import '../config/google_oauth.dart';

class AuthService {
  AuthService._();
  static final AuthService instance = AuthService._();

  final FirebaseAuth _auth = FirebaseAuth.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn.instance;
  bool _initialized = false;

  Stream<User?> authStateChanges() => _auth.authStateChanges();

  Future<void> _ensureInitialized() async {
    if (_initialized || kIsWeb) return;
    await _googleSignIn.initialize(
      serverClientId: kGoogleServerClientId.isEmpty ? null : kGoogleServerClientId,
      clientId: kGoogleClientIdIOS,
    );
    _initialized = true;
  }

  /// Signs in with Google on web and mobile. Returns the Firebase [User] or null if cancelled.
  Future<User?> signInWithGoogle() async {
    try {
      if (kIsWeb) {
        final googleProvider = GoogleAuthProvider();
        final credential = await _auth.signInWithPopup(googleProvider);
        return credential.user;
      } else {
        await _ensureInitialized();
        if (!_googleSignIn.supportsAuthenticate()) {
          // Should not happen on mobile, but defensively return null.
          return null;
        }
        final GoogleSignInAccount googleUser = await _googleSignIn.authenticate();
        // googleUser.authentication is synchronous in v7
        final GoogleSignInAuthentication googleAuth = googleUser.authentication;

        final credential = GoogleAuthProvider.credential(
          idToken: googleAuth.idToken,
        );

        final userCred = await _auth.signInWithCredential(credential);
        return userCred.user;
      }
    } on FirebaseAuthException catch (e) {
      debugPrint('FirebaseAuthException: ${e.code} - ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('Unknown sign-in error: $e');
      rethrow;
    }
  }

  /// Email & password sign in
  Future<User?> signInWithEmailAndPassword(String email, String password) async {
    final cred = await _auth.signInWithEmailAndPassword(email: email, password: password);
    return cred.user;
  }

  /// Email & password sign up
  Future<User?> signUpWithEmailAndPassword(String email, String password) async {
    final cred = await _auth.createUserWithEmailAndPassword(email: email, password: password);
    return cred.user;
  }

  /// Send password reset email
  Future<void> sendPasswordResetEmail(String email) async {
    await _auth.sendPasswordResetEmail(email: email);
  }

  /// Signs out from Google (mobile) and Firebase.
  Future<void> signOut() async {
    try {
      if (!kIsWeb) {
        await _googleSignIn.signOut();
      }
    } catch (e) {
      debugPrint('Google signOut error: $e');
    } finally {
      await _auth.signOut();
    }
  }
}

