import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import '../services/auth_service.dart';
import '../screens/home_page.dart';
import '../screens/login_screen.dart';

class AuthGate extends StatelessWidget {
  const AuthGate({super.key});

  @override
  Widget build(BuildContext context) {
    final auth = AuthService.instance;

    return StreamBuilder<User?>
      (
      stream: auth.authStateChanges(),
      builder: (context, snapshot) {
        final user = snapshot.data;

        // Not signed in
        if (user == null) {
          return const LoginScreen();
        }

        // Signed in -> return your app's home screen; it owns the AppBar
        return const MyHomePage(title: 'Numerology App');
      },
    );
  }
}

